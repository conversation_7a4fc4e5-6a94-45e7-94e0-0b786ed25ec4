# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include .tfvars files you do wish to add to version control using negated pattern
# !example.tfvars

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore Mac .DS_Store files
.DS_Store

# Ignore Windows Thumbs.db files
Thumbs.db

# Ignore editor and IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore log files
*.log

# Ignore backup files
*.backup
*.bak

# Ignore temporary files
*.tmp
*.temp

# Ignore plan files (optional - some teams prefer to commit these)
*.tfplan
*.tfplan.*

# Ignore lock files (optional - many teams commit .terraform.lock.hcl)
# .terraform.lock.hcl

# Ignore environment-specific files
.env
.env.local
.env.*.local

# Ignore auto-generated documentation
docs/

# Ignore test results
test-results/
*.test
