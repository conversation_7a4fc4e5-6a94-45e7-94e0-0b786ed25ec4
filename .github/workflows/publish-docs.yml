name: Publish docs via GitHub Pages
on:
  workflow_dispatch:
  push:
    branches:
      - main
      - master

permissions:
  contents: read

jobs:
  build:
    name: Deploy docs
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout main
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install mkdocs-material==9.5.26 \
            mkdocs-include-markdown-plugin==6.2.0 \
            mkdocs-awesome-pages-plugin==2.9.2

      - name: git config
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - run: mkdocs gh-deploy --force
