# Local Development

## Documentation Site

In order to run the documentation site locally, you will need to have the following installed locally:

- [Python 3.x](https://www.python.org/downloads/)
- [mkdocs](https://www.mkdocs.org/user-guide/installation/)
- The following pip packages for mkdocs (i.e. - `pip install ...`)
    - `mkdocs-material`
    - `mkdocs-include-markdown-plugin`
    - `mkdocs-awesome-pages-plugin`

To run the documentation site locally, run the following command from the root of the repository:

```bash
mkdocs serve
```

Opening the documentation at the link posted in the terminal output (i.e. - [http://127.0.0.1:8000/terraform-aws-eks/](http://127.0.0.1:8000/terraform-aws-eks/))
